<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pausenliga Tippspiel</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="media-quarys.css">
    <!-- Google Fonts: Poppins -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    <!-- Optional: Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>

    <div class="container">
    <img id="gauss-logo" src="Gauss-Logo-Europa-round-main.png">

    <header class="header-image">
    </header>

    <header class="hero" id="webseite-header">
        <h1>Willkommen zur Webseite der Pausenliga</h1>
        <p>Hier erfährst du alles über die Team der Pausen Liga am Carl Friedrich Gauss Gymnasium</p>
    </header>

    <!-- Top Navigation -->
    <nav class="nav">
        <div class="logo">Gauß Gymnasium Pausenliga</div>
        <div class="nav-right">
            <button id="darkModeToggle" class="dark-mode-toggle" title="Dark Mode umschalten">
                <i class="fas fa-moon"></i>
            </button>
            <button id="loginBtn" class="nav-btn">Anmelden</button>
        </div>
    </nav>
    </div>

    <main class="main-content">
        <section class="info-section">
            <h2>Was ist die Pausenliga?</h2>
            <p>Die Pausenliga ist die Fußball-Liga des Carl Friedrich Gauss Gymnasiums.<p>
        </section>

        <section class="teams-section">
            <h2>Unsere Teams 2025</h2>
            <div class="team-card">
                <img src="Media/Bild-1.jpeg" alt="Team 7A" class="team-img">
                <h3>5A</h3>
                <p>Die jungen Talente der 7A sind heiß auf den Sieg!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-2.jpeg" alt="Team 7B" class="team-img">
                <h3>5B</h3>
                <p>Die 8B hat sich viel vorgenommen und will ganz oben mitspielen!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-3.jpeg" alt="Team 7C" class="team-img">
                <h3>5C</h3>
                <p>Die 9C ist bekannt für ihre Teamarbeit und Fairness.</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-4.jpeg" alt="Team 6A" class="team-img">
                <h3>6A</h3>
                <p>Die 10A hat Erfahrung und kämpft um jeden Punkt!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-5.jpeg" alt="Team 6B" class="team-img">
                <h3>6B</h3>
                <p>Unsere Lehrer geben alles, um die Schüler herauszufordern!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-6.jpeg" alt="Team 6C" class="team-img">
                <h3>6C</h3>
                <p>Die 12B ist das Team der Oberstufe und will den Titel holen!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-7.jpeg" alt="Team 6C" class="team-img">
                <h3>7A</h3>
                <p>Die 12B ist das Team der Oberstufe und will den Titel holen!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-8.jpeg" alt="Team 6C" class="team-img">
                <h3>7B</h3>
                <p>Die 12B ist das Team der Oberstufe und will den Titel holen!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-9.jpeg" alt="Team 6C" class="team-img">
                <h3>7C</h3>
                <p>Die 12B ist das Team der Oberstufe und will den Titel holen!</p>
            </div>
        </section>
    </main>

    <!-- Hero Section -->
    <header class="hero">
        <h1>Tippe auf die Spiele der Pausenliga!</h1>
        <p>Wer holt den Sieg in der großen Pause? Gib deinen Tipp ab und vergleiche dich mit deinen Mitschülern – ganz ohne Einsatz, nur zum Spaß!</p>
    </header>

    <!-- Live Betting Section -->
    <main>
        <section class="live-section">
            <h2>Aktuelle Pausenliga-Spiele</h2>
            <div class="live-card">
                <div>
                    <div class="match">7A vs. 8B</div>
                    <div class="sport">Fußball</div>
                </div>
                <div class="odds-group">
                    <div class="odd" id="odds1-1">0 Tipps für 7A</div>
                    <div class="odd" id="odds1-2">0 Tipps für 8B</div>
                </div>
            </div>
            <div class="live-card">
                <div>
                    <div class="match">9C vs. 10A</div>
                    <div class="sport">Fußball</div>
                </div>
                <div class="odds-group">
                    <div class="odd" id="odds2-1">0 Tipps für 9C</div>
                    <div class="odd" id="odds2-2">0 Tipps für 10A</div>
                </div>
            </div>
            <div class="live-card">
                <div>
                    <div class="match">Lehrerteam vs. 12B</div>
                    <div class="sport">Fußball</div>
                </div>
                <div class="odds-group">
                    <div class="odd" id="odds3-1">0 Tipps für Lehrerteam</div>
                    <div class="odd" id="odds3-2">0 Tipps für 12B</div>
                </div>
            </div>
        </section>

        <!-- Voting Section -->
        <section class="vote-section">
            <div class="vote-header">
                <h2>Wer gewinnt das Topspiel?</h2>
                <div class="matchup-menu">
                    <button type="button" class="hamburger-btn" id="matchupToggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                    <div class="matchup-dropdown" id="matchupDropdown">
                        <div class="matchup-option" data-matchup="match1">7A vs 8B</div>
                        <div class="matchup-option" data-matchup="match2">9C vs 10A</div>
                        <div class="matchup-option" data-matchup="match3">Lehrerteam vs 12B</div>
                    </div>
                </div>
            </div>
            <form id="betForm">
                <input type="text" id="name" name="name" placeholder="Dein Name" required>
                <select id="team" name="team" required>
                    <option value="">--Team wählen--</option>
                    <option value="odds1-1">7A</option>
                    <option value="odds1-2">8B</option>
                    <option value="odds2-1">9C</option>
                    <option value="odds2-2">10A</option>
                    <option value="odds3-1">Lehrerteam</option>
                    <option value="odds3-2">12B</option>
                </select>
                <button class="tipp_abgeben" type="submit">Tipp abgeben</button>
            </form>
            <div id="results">
                <div>
                    <span>7A:</span>
                    <div class="bar-bg">
                        <div id="barA" class="bar"></div>
                    </div>
                    <span id="percentA">0%</span>
                </div>
                <div>
                    <span>8B:</span>
                    <div class="bar-bg">
                        <div id="barB" class="bar"></div>
                    </div>
                    <span id="percentB">0%</span>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer>
        <div>
            <a href="#">Regeln</a> | <a href="#">Kontakt</a> | <a href="#">Impressum</a>
        </div>
        <p>&copy; 2025 Pausenliga Tippspiel. Nur für Schüler:innen und Lehrer:innen der Schule. Kein echtes Geld, nur Spaß!</p>
        <div class="opensource-section">
            <button id="creditsToggle" class="credits-toggle-btn">
                <i class="fas fa-info-circle"></i>
            </button>
            <p>&copy; Diese Webseite ist ein Opensource Project von Schülern des Informatik Kurses in der 9ten Stufe 2025.</p>
        </div>
        <div class="credits" id="creditsPanel">
            <p><strong>Entwickelt von:</strong></p>
            <p>Informatik Lehrern <em>des Carl Friedrich Gauß Gymnasiums</em></p>
            <p> & </p>
            <p>Nicolae Toaca & Alexander Straub</p>
            <p><em>Schüler des Informatik Kurses, Carl Friedrich Gauß Gymnasium</em></p>
        </div>
    </footer>

    <!-- Login Modal -->
    <div id="loginModal" class="modal-overlay">
        <div class="modal-container">
            <!-- Modal Header -->
            <div class="modal-header">
                <h2 class="modal-title">
                    <i class="fas fa-user-circle"></i>
                    Anmelden
                </h2>
                <button id="closeModal" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="modal-content">
                <!-- Traditional Login Form -->
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i>
                            Benutzername
                        </label>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            class="form-input"
                            placeholder="Dein Benutzername"
                            required
                        >
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i>
                            Passwort
                        </label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-input"
                            placeholder="Dein Passwort"
                            required
                        >
                    </div>

                    <button type="submit" class="login-btn login-btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Einloggen
                    </button>
                </form>

                <!-- Divider -->
                <div class="login-divider">
                    <span class="divider-text">oder</span>
                </div>

                <!-- SSO Login Button -->
                <button id="ssoLoginBtn" class="login-btn login-btn-sso">
                    <i class="fas fa-building"></i>
                    Mit IServ anmelden (SSO)
                </button>

                <!-- Additional Options -->
                <div class="login-footer">
                    <a href="#" class="login-link">Passwort vergessen?</a>
                </div>
            </div>
        </div>
    </div>

    <script src="website.js"></script>
</body>
</html>