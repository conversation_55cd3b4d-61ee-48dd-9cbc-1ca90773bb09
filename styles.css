body {
    font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    margin: 0;
    background: #fff;
    color: #1a1a1a;
    min-height: 100vh;
    text-align: center;
    background: linear-gradient(135deg, #e8e8e8 0%, #d0d0d0 25%, #c0c0c0 50%, #d0d0d0 75%, #e8e8e8 100%);
    position: relative;
    overflow-x: hidden;
}

/* Navigation Styles */
.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 1rem 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    position: relative;
    margin-top: 20px;
    transition: all 0.3s ease;
    border-bottom: none;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-right: 1200px;
}

.nav-btn {
    background: linear-gradient(135deg, #fff 0%, #f5f5f5 100%);
    color: #333;
    border: 2px solid #8b0000;
    padding: 0.6rem 1.2rem;
    border-radius: 12px;
    margin-left: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-btn:hover {
    background: linear-gradient(135deg, #8b0000 0%, #cc0000 100%);
    color: #fff;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(139, 0, 0, 0.3);
}

.nav-btn-primary {
    background: #ff5252;
    color: #fff;
}

.nav-btn-primary:hover {
    background: #8b0000;
    color: #fff;
}

/* Dark Mode Toggle */
.dark-mode-toggle {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.6rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dark-mode-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 1rem 3rem 1rem;
    color: #68140f;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modern minimalistic 3D cuboid for hero paragraph - Light Mode */
.hero p {
    /* Text styling for light mode */
    color: #1a1a1a;
    font-size: 1.1rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;

    /* Cuboid container styling */
    max-width: 500px;
    margin: 2rem auto;
    padding: 1.8rem 2.2rem;

    /* 3D cuboid appearance - Light mode (white interior) */
    background: linear-gradient(145deg, #ffffff, #f5f5f5);
    border: 1px solid #ccc;
    border-radius: 8px;
    position: relative;

    /* 3D depth effect with multiple shadows */
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);

    /* 3D transform for subtle perspective */
    transform: perspective(1000px) rotateX(2deg) rotateY(-1deg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced 3D hover effect - Light mode */
.hero p:hover {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(-2px);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.2),
        0 16px 32px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.15),
        0 0 20px rgba(139, 0, 0, 0.2);

    background: linear-gradient(145deg, #fafafa, #f0f0f0);
    border-color: #aaa;
}

/* Subtle tech-inspired accent */
.hero p::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(139, 0, 0, 0.6), transparent);
    border-radius: 8px 8px 0 0;
}

/* Reset hero positioning */
.hero {
    position: relative;
}

.cta-btn {
    background: linear-gradient(90deg, #ff5252, #b71c1c);
    color: #fff;
    border: none;
    padding: 1rem 2.5rem;
    border-radius: 999px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 4px 24px #b71c1c44;
    transition: background 0.2s, transform 0.2s;
}

.cta-btn:hover {
    background: linear-gradient(90deg, #b71c1c, #ff5252);
    color: #fff;
    transform: scale(1.05);
}

/* Live Section */
.live-section {
    max-width: 700px;
    margin: 2rem auto;
    padding: 1rem;
}

.live-section h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #1a1a1a !important;
    font-weight: 600;
}

.live-card {
    background: #fff;
    border-radius: 18px;
    padding: 1.2rem 1.5rem;
    margin-bottom: 1.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 12px #ff525222;
    border: 1px solid #ff5252;
    transition: box-shadow 0.2s, transform 0.2s;
}

.live-card:hover {
    box-shadow: 0 4px 24px #b71c1c55;
    transform: scale(1.02);
}

.match {
    font-weight: bold;
    font-size: 1.1rem;
    color: #b71c1c;
}

.sport {
    color: #ff5252;
    font-size: 0.95rem;
}

.odds-group {
    display: flex;
    gap: 0.7rem;
}

.odd {
    background: #fff;
    color: #b71c1c;
    padding: 0.6rem 1.1rem;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.1rem;
    box-shadow: 0 2px 8px #ff525222;
    border: 1px solid #ff5252;
    transition: background 0.2s, color 0.2s, transform 0.2s;
    cursor: pointer;
}

.odd:hover {
    background: #b71c1c;
    color: #fff;
    transform: scale(1.08);
}

/* Voting Section */
.vote-section {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem 1rem;
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 2px 12px #ff525222;
    border: 1px solid #ff5252;
}

/* Vote section header with hamburger menu */
.vote-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.2rem;
    position: relative;
}

.vote-section h2 {
    color: #b71c1c;
    margin: 0;
}

/* Hamburger menu styling */
.matchup-menu {
    position: relative;
}

.hamburger-btn {
    background: none;
    border: 2px solid #8b0000;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
    padding: 0;
}

.hamburger-btn:hover {
    background: rgba(139, 0, 0, 0.1);
    transform: scale(1.05);
}

.hamburger-btn span {
    width: 20px;
    height: 2px;
    background: #8b0000;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.hamburger-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-btn.active span:nth-child(2) {
    opacity: 0;
}

.hamburger-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Dropdown menu */
.matchup-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    background: #fff;
    border: 2px solid #8b0000;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(139, 0, 0, 0.2);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 100;
}

.matchup-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.matchup-option {
    padding: 12px 16px;
    color: #8b0000;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(139, 0, 0, 0.1);
}

.matchup-option:last-child {
    border-bottom: none;
}

.matchup-option:hover {
    background: rgba(139, 0, 0, 0.1);
    color: #cc0000;
}

.matchup-option:first-child {
    border-radius: 10px 10px 0 0;
}

.matchup-option:last-child {
    border-radius: 0 0 10px 10px;
}

#betForm {
    display: flex;
    gap: 0.7rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

#betForm input, #betForm select {
    padding: 0.6rem 1rem;
    border-radius: 8px;
    border: 1px solid #ff5252;
    font-size: 1rem;
    background: #fff;
    color: #b71c1c;
    outline: none;
    box-shadow: 0 2px 8px #ff525211;
    transition: box-shadow 0.2s, border 0.2s;
}

#betForm input:focus, #betForm select:focus {
    box-shadow: 0 2px 12px #b71c1c55;
    border: 1.5px solid #b71c1c;
}

#betForm button {
    background: linear-gradient(90deg, #ff5252, #b71c1c);
    color: #fff;
    border: none;
    padding: 0.6rem 1.5rem;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}

#betForm button:hover {
    background: linear-gradient(90deg, #b71c1c, #ff5252);
    color: #fff;
}

/* Modern expensive percentage bar styling */
#results > div {
    display: flex;
    align-items: center;
    margin-bottom: 1.2rem;
    gap: 1rem;
    padding: 0.8rem 1rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 248, 248, 0.9));
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(139, 0, 0, 0.1);
    transition: all 0.3s ease;
}

#results > div:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.bar-bg {
    background: linear-gradient(135deg, #f8f8f8, #e8e8e8);
    border-radius: 20px;
    height: 8px;
    width: 200px;
    overflow: hidden;
    margin: 0 0.8rem;
    border: none;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.bar-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.bar {
    border-radius: 20px;
    height: 100%;
    width: 0;
    background: linear-gradient(135deg, #8b0000 0%, #cc0000 50%, #8b0000 100%);
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 0 10px rgba(139, 0, 0, 0.3);
    overflow: hidden;
}

.bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), transparent);
    border-radius: 20px 20px 0 0;
}

.bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 30%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%);
    animation: smoothShimmer 3s ease-in-out infinite;
    border-radius: 20px;
    transform: skewX(-20deg);
}

@keyframes smoothShimmer {
    0% {
        left: -50%;
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        left: 120%;
        opacity: 0;
    }
}

#results span {
    font-weight: 600;
    color: #1a1a1a;
    font-size: 0.95rem;
    min-width: 40px;
}

footer {
    background: #b71c1c;
    color: #fff;
    text-align: center;
    padding: 2rem 1rem 1rem 1rem;
    margin-top: 3rem;
    border-radius: 18px 18px 0 0;
    font-size: 0.95rem;
}

footer a {
    color: #fff;
    text-decoration: underline;
    margin: 0 0.5rem;
    transition: color 0.2s;
}

footer a:hover {
    color: #ff5252;
}

/* Opensource section with toggle button */
.opensource-section {
    position: relative;
    margin-bottom: 1rem;
    text-align: center;
}

.opensource-section p {
    margin: 0;
}

.credits-toggle-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 1rem;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

.credits-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

.credits-toggle-btn.active {
    background: rgba(255, 255, 255, 0.4);
    transform: rotate(180deg);
}

/* Credits section styling - hidden by default */
.credits {
    margin-top: 1rem;
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 0.9rem;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
}

.credits.active {
    max-height: 200px;
    opacity: 1;
    padding: 1.5rem;
}

.credits p {
    margin: 0.5rem 0;
}

.credits strong {
    color: #fff;
    font-weight: 600;
}

.credits em {
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
    font-size: 0.85rem;
}

.main-content {
    text-align: center;
    background: linear-gradient(135deg, #8b0000 0%, #cc0000 50%, #8b0000 100%);
    max-width: 1300px;
    color: white;
    margin-left: 300px;
    padding-top: 50px;
    padding-left: 50px;
    padding-right: 50px;
    margin-top: 100px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(139, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#website-header {
    color: #68140f;
}

#gauss-logo {
    position: absolute !important;
    display: flex !important;
    margin-left: 100px ;
    width: 230px;
    margin-top: 10px;
}

.container {
    background-image: url(headerfussball.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: relative;
}



/* Animierte Team-Karten */
.team-card {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(139, 0, 0, 0.15);
    margin: 1.2rem 0.7rem;
    padding: 1.2rem 1.5rem;
    display: inline-block;
    min-width: 220px;
    max-width: 260px;
    vertical-align: top;
    transition: all 0.3s cubic-bezier(.4,2,.6,1);
    cursor: pointer;
    border: 2px solid rgba(139, 0, 0, 0.3);
    animation: cardIn 0.7s cubic-bezier(.4,2,.6,1);
    position: relative;
    overflow: hidden;
    color: #1a1a1a;
}

.team-card h3 {
    color: #1a1a1a;
}

.team-card p {
    color: #333;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(139, 0, 0, 0.1), transparent);
    transition: left 0.5s ease;
}
.team-card:hover {
    transform: scale(1.05) translateY(-5px);
    box-shadow: 0 12px 40px rgba(139, 0, 0, 0.25);
    border-color: #8b0000;
}

.team-card:hover::before {
    left: 100%;
}

/* Karten-Einblend-Animation */
@keyframes cardIn {
    0% { opacity: 0; transform: scale(0.8) translateY(40px);}
    100% { opacity: 1; transform: scale(1) translateY(0);}
}

/* Tipp-Animation */
.tipp-anim {
    display: inline-block;
    animation: tippBounce 0.5s;
}
.tipp-bounce {
    animation: tippBounce 0.5s;
}
@keyframes tippBounce {
    0% { transform: scale(1);}
    30% { transform: scale(1.3);}
    60% { transform: scale(0.95);}
    100% { transform: scale(1);}
}

/* Fortschrittsbalken sanft animiert */
.bar {
    transition: width 0.7s cubic-bezier(.4,2,.6,1), background 0.3s;
    background: linear-gradient(90deg, #b71c1c 0%, #8b0000 100%);
}

/* Buttons mit Bounce und Farbverlauf */
.cta-btn, .odd, #betForm button {
    font-family: 'Poppins', Arial, sans-serif;
    font-weight: 700;
    letter-spacing: 0.03em;
    box-shadow: 0 2px 12px #b71c1c33;
    transition: background 0.2s, color 0.2s, transform 0.2s, box-shadow 0.2s;
}
.cta-btn:hover, .odd:hover, #betForm button:hover {
    animation: btnBounce 0.4s;
    background: linear-gradient(90deg, #8b0000, #b71c1c);
    color: #fff;
    box-shadow: 0 6px 24px #8b000055;
}
@keyframes btnBounce {
    0% { transform: scale(1);}
    40% { transform: scale(1.12);}
    60% { transform: scale(0.96);}
    100% { transform: scale(1);}
}

.team-img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-radius: 16px 16px 0 0;
    margin-bottom: 0.7rem;
    box-shadow: 0 2px 8px #b71c1c33;
    border: none;
    display: block;
    margin-left: 0;
    margin-right: 0;
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: #1a1a1a;
    color: #e0e0e0;
}

body.dark-mode .nav {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-bottom: 2px solid #8b0000;
}

body.dark-mode .logo {
    color: #e0e0e0;
}

body.dark-mode .nav-btn {
    background: #3d3d3d;
    color: #e0e0e0;
    border: 1px solid #555;
}

body.dark-mode .nav-btn:hover {
    background: #555;
    color: #fff;
}

body.dark-mode .nav-btn-primary {
    background: #4a4a4a;
    color: #e0e0e0;
}

body.dark-mode .nav-btn-primary:hover {
    background: #666;
    color: #fff;
}

body.dark-mode .dark-mode-toggle {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

body.dark-mode .dark-mode-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

body.dark-mode .hero {
    color: #e0e0e0;
}

body.dark-mode .hero h1 {
    color: #fff;
}

body.dark-mode .hero p {
    color: #ccc;
}

body.dark-mode .main-content {
    background-color: #3d3d3d;
    color: #e0e0e0;
}

body.dark-mode .live-section h2 {
    color: #1a1a1a !important;
}

body.dark-mode .live-card {
    background: #2d2d2d;
    border-color: #555;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

body.dark-mode .live-card:hover {
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5);
}

body.dark-mode .match {
    color: #e0e0e0;
}

body.dark-mode .sport {
    color: #aaa;
}

body.dark-mode .odd {
    background: #3d3d3d;
    color: #e0e0e0;
    border-color: #555;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

body.dark-mode .odd:hover {
    background: #555;
    color: #fff;
}

body.dark-mode .vote-section {
    background: #2d2d2d;
    border-color: #555;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

body.dark-mode .vote-section h2 {
    color: #e0e0e0;
}

body.dark-mode #betForm input,
body.dark-mode #betForm select {
    background: #3d3d3d;
    color: #e0e0e0;
    border-color: #555;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark-mode #betForm input:focus,
body.dark-mode #betForm select:focus {
    border-color: #777;
    box-shadow: 0 2px 12px rgba(255, 255, 255, 0.1);
}

/* Dark mode styles for modern percentage bars */
body.dark-mode #results > div {
    background: linear-gradient(135deg, rgba(45, 45, 45, 0.9), rgba(26, 26, 26, 0.9));
    border-color: rgba(139, 0, 0, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

body.dark-mode #results > div:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

body.dark-mode .bar-bg {
    background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.4);
}

body.dark-mode .bar-bg::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

body.dark-mode #results span {
    color: #e0e0e0;
}

/* Dark mode styles for hamburger menu */
body.dark-mode .matchup-dropdown {
    background: #2d2d2d;
    border-color: #8b0000;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

body.dark-mode .matchup-option {
    color: #8b0000;
    border-bottom-color: rgba(139, 0, 0, 0.2);
}

body.dark-mode .matchup-option:hover {
    background: rgba(139, 0, 0, 0.2);
    color: #ff4444;
}

body.dark-mode .team-card {
    background: #3d3d3d;
    border-color: #555;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
    color: #fff;
}

body.dark-mode .team-card h3 {
    color: #fff;
}

body.dark-mode .team-card p {
    color: #e0e0e0;
}

body.dark-mode .team-card:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    border-color: #777;
}

body.dark-mode footer {
    background: #2d2d2d;
    color: #e0e0e0;
}

body.dark-mode footer a {
    color: #ccc;
}

body.dark-mode footer a:hover {
    color: #fff;
}

/* Dark mode credits styling */
body.dark-mode .credits-toggle-btn {
    background: rgba(224, 224, 224, 0.1);
    border-color: rgba(224, 224, 224, 0.2);
    color: #e0e0e0;
}

body.dark-mode .credits-toggle-btn:hover {
    background: rgba(224, 224, 224, 0.2);
    border-color: rgba(224, 224, 224, 0.3);
}

body.dark-mode .credits-toggle-btn.active {
    background: rgba(224, 224, 224, 0.3);
}

body.dark-mode .credits {
    border-top-color: rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

body.dark-mode .credits strong {
    color: #e0e0e0;
}

body.dark-mode .credits em {
    color: rgba(224, 224, 224, 0.7);
}

/* ===== LOGIN MODAL STYLES ===== */

/* Modal Overlay - Dark semi-transparent background */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

/* Show modal when active */
.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Modal Container */
.modal-container {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 420px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8) translateY(20px);
    transition: all 0.3s ease;
    border: 2px solid #cf2e2e;
}

/* Modal container animation when active */
.modal-overlay.active .modal-container {
    transform: scale(1) translateY(0);
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #cf2e2e, #b71c1c);
    color: #fff;
    border-radius: 14px 14px 0 0;
}

.modal-title {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: #fff;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Modal Content */
.modal-content {
    padding: 2rem;
}

/* Login Form */
.login-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
    font-size: 0.95rem;
}

.form-input {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    background: #fff;
    color: #333;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #cf2e2e;
    box-shadow: 0 0 0 3px rgba(207, 46, 46, 0.1);
    transform: translateY(-1px);
}

.form-input:hover {
    border-color: #cf2e2e;
}

/* Login Buttons */
.login-btn {
    width: 100%;
    padding: 0.9rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-decoration: none;
    box-sizing: border-box;
}

/* Primary Login Button */
.login-btn-primary {
    background: linear-gradient(135deg, #cf2e2e, #b71c1c);
    color: #fff;
    box-shadow: 0 4px 15px rgba(207, 46, 46, 0.3);
}

.login-btn-primary:hover {
    background: linear-gradient(135deg, #b71c1c, #8b0000);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(207, 46, 46, 0.4);
}

.login-btn-primary:active {
    transform: translateY(0);
}

/* SSO Login Button */
.login-btn-sso {
    background: #fff;
    color: #333;
    border: 2px solid #333;
    margin-top: 1rem;
}

.login-btn-sso:hover {
    background: #333;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Divider */
.login-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.login-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
}

.divider-text {
    background: #fff;
    padding: 0 1rem;
    color: #666;
    font-size: 0.9rem;
    position: relative;
}

/* Login Footer */
.login-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.login-link {
    color: #cf2e2e;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.login-link:hover {
    color: #b71c1c;
    text-decoration: underline;
}

/* Dark Mode Styles for Modal */
body.dark-mode .modal-container {
    background: #2d2d2d;
    border-color: #555;
}

body.dark-mode .modal-header {
    background: linear-gradient(135deg, #3d3d3d, #2d2d2d);
}

body.dark-mode .form-label {
    color: #e0e0e0;
}

body.dark-mode .form-input {
    background: #3d3d3d;
    border-color: #555;
    color: #e0e0e0;
}

body.dark-mode .form-input:focus {
    border-color: #777;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

body.dark-mode .form-input:hover {
    border-color: #777;
}

body.dark-mode .login-btn-sso {
    background: #3d3d3d;
    color: #e0e0e0;
    border-color: #555;
}

body.dark-mode .login-btn-sso:hover {
    background: #555;
    color: #fff;
}

body.dark-mode .divider-text {
    background: #2d2d2d;
    color: #aaa;
}

body.dark-mode .login-divider::before {
    background: #555;
}

body.dark-mode .login-footer {
    border-color: #555;
}

body.dark-mode .login-link {
    color: #ff6b6b;
}

body.dark-mode .login-link:hover {
    color: #ff5252;
}

/* Dark mode styles for 3D cuboid */
body.dark-mode .hero p {
    /* Text styling for dark mode */
    color: #f0f0f0;

    /* 3D cuboid appearance - Dark mode (black interior) */
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border: 1px solid #444;

    /* 3D depth effect with multiple shadows for dark mode */
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.4),
        0 8px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.4);
}

/* Enhanced 3D hover effect - Dark mode */
body.dark-mode .hero p:hover {
    background: linear-gradient(145deg, #2d2d2d, #1d1d1d);
    border-color: #555;

    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.5),
        0 16px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(0, 0, 0, 0.5),
        0 0 20px rgba(139, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 480px) {
    .modal-container {
        width: 95%;
        margin: 1rem;
    }

    .modal-content {
        padding: 1.5rem;
    }

    .modal-header {
        padding: 1rem 1.5rem;
    }

    .modal-title {
        font-size: 1.2rem;
    }

    .form-input {
        padding: 0.7rem;
    }

    .login-btn {
        padding: 0.8rem 1rem;
    }
}

/* Animation for form validation feedback */
.form-input.error {
    border-color: #e74c3c;
    animation: shake 0.3s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Loading state for buttons */
.login-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.login-btn.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

