from flask import Flask, redirect, url_for, session
from authlib.integrations.flask_client import OAuth
import os

app = Flask(__name__)
app.secret_key = os.urandom(24)

# OAuth-Konfiguration
oauth = OAuth(app)
oauth.register(
    name='google',
    client_id='DEIN_GOOGLE_CLIENT_ID',
    client_secret='DEIN_GOOGLE_CLIENT_SECRET',
    server_metadata_url='https://accounts.google.com/.well-known/openid-configuration',
    client_kwargs={
        'scope': 'openid email profile'
    }
)

@app.route('/')
def homepage():
    user = session.get('user')
    if user:
        return f'Hallo {user["name"]}! <a href="/logout">Logout</a>'
    return '<a href="/login">Mit Google anmelden</a>'

@app.route('/login')
def login():
    redirect_uri = url_for('auth_callback', _external=True)
    return oauth.google.authorize_redirect(redirect_uri)

@app.route('/auth/callback')
def auth_callback():
    token = oauth.google.authorize_access_token()
    userinfo = oauth.google.parse_id_token(token)
    session['user'] = userinfo
    return redirect('/')

@app.route('/logout')
def logout():
    session.pop('user', None)
    return redirect('/')

if __name__ == '__main__':
    app.run(debug=True)
