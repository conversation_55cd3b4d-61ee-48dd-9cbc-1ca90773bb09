// Tipp-<PERSON><PERSON><PERSON> für alle Spiele/Teams
const tipps = {
    "odds1-1": 0, // 7A (sollte es zumindestens sein aber der wunderbare Nico hat was falsches rein geschrieben, ja mann sorry :( )
    "odds1-2": 0, // 7B
    "odds2-1": 0, // 7C
    "odds2-2": 0, // 6A
    "odds3-1": 0, // 6B
    "odds3-2": 0  // 6C
};

// Team names mapping
const teamNames = {
    "odds1-1": "7A",
    "odds1-2": "8B",
    "odds2-1": "9C",
    "odds2-2": "10A",
    "odds3-1": "Lehrerteam",
    "odds3-2": "12B"
};

// Anzeige der Tipp-Anzahl mit Animation und Team-Namen
function updateOddsFields() {
    Object.keys(tipps).forEach(id => {
        const el = document.getElementById(id);
        if (el) {
            const count = tipps[id];
            const teamName = teamNames[id];
            const displayText = `<span class="tipp-anim"><b>${count}</b> Tipps für ${teamName}</span>`;
            el.innerHTML = displayText;
            el.classList.remove("tipp-bounce");
            void el.offsetWidth; // Trigger reflow for animation
            el.classList.add("tipp-bounce");
        }
    });
}

// Matchup definitions
const matchups = {
    "odds1-1": { opponent: "odds1-2", team1: "7A", team2: "8B" },
    "odds1-2": { opponent: "odds1-1", team1: "7A", team2: "8B" },
    "odds2-1": { opponent: "odds2-2", team1: "9C", team2: "10A" },
    "odds2-2": { opponent: "odds2-1", team1: "9C", team2: "10A" },
    "odds3-1": { opponent: "odds3-2", team1: "Lehrerteam", team2: "12B" },
    "odds3-2": { opponent: "odds3-1", team1: "Lehrerteam", team2: "12B" }
};

// Voting-System für alle Matchups
const votes = {
    "odds1-1": 0, "odds1-2": 0,
    "odds2-1": 0, "odds2-2": 0,
    "odds3-1": 0, "odds3-2": 0
};

let currentMatchup = null;

function updateBars(matchupKey) {
    if (!matchupKey || !matchups[matchupKey]) return;

    const matchup = matchups[matchupKey];

    // Find the correct keys for both teams in this matchup
    let team1Key, team2Key;

    // Find both team keys for this specific matchup
    Object.keys(matchups).forEach(key => {
        if (matchups[key].team1 === matchup.team1 && matchups[key].team2 === matchup.team2) {
            if (teamNames[key] === matchup.team1) {
                team1Key = key;
            } else if (teamNames[key] === matchup.team2) {
                team2Key = key;
            }
        }
    });

    const team1Votes = votes[team1Key] || 0;
    const team2Votes = votes[team2Key] || 0;
    const total = team1Votes + team2Votes;

    // Update team labels
    const team1Label = document.querySelector('#results div:first-child span:first-child');
    const team2Label = document.querySelector('#results div:last-child span:first-child');

    if (team1Label) team1Label.textContent = matchup.team1 + ':';
    if (team2Label) team2Label.textContent = matchup.team2 + ':';

    // Calculate percentages - handle zero total case
    let team1Percent, team2Percent;

    if (total === 0) {
        team1Percent = 0;
        team2Percent = 0;
    } else {
        team1Percent = Math.round((team1Votes / total) * 100);
        team2Percent = Math.round((team2Votes / total) * 100);

        // Ensure percentages add up to 100% (handle rounding issues)
        if (team1Percent + team2Percent !== 100 && total > 0) {
            if (team1Votes > team2Votes) {
                team1Percent = 100 - team2Percent;
            } else {
                team2Percent = 100 - team1Percent;
            }
        }
    }

    // Update bars and percentages
    const barA = document.getElementById("barA");
    const barB = document.getElementById("barB");
    const percentA = document.getElementById("percentA");
    const percentB = document.getElementById("percentB");

    if (barA) barA.style.width = team1Percent + "%";
    if (barB) barB.style.width = team2Percent + "%";
    if (percentA) percentA.textContent = team1Percent + "%";
    if (percentB) percentB.textContent = team2Percent + "%";

    currentMatchup = matchupKey;

    console.log(`Debug: ${matchup.team1} (${team1Votes}) vs ${matchup.team2} (${team2Votes}) = ${team1Percent}% vs ${team2Percent}%`);
}

document.getElementById("betForm").addEventListener("submit", function(e) {
    e.preventDefault();
    const team = document.getElementById("team").value;
    if (team && tipps[team] !== undefined) {
        tipps[team]++;
        if (votes[team] !== undefined) votes[team]++;
        updateBars(team);
        updateOddsFields();
        this.reset();
    }
});

// Dark Mode Toggle Functionality
function initDarkMode() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    const body = document.body;
    const icon = darkModeToggle.querySelector('i');

    // Check for saved dark mode preference or default to light mode
    const isDarkMode = localStorage.getItem('darkMode') === 'true';

    if (isDarkMode) {
        body.classList.add('dark-mode');
        icon.classList.remove('fa-moon');
        icon.classList.add('fa-sun');
    }

    darkModeToggle.addEventListener('click', function() {
        body.classList.toggle('dark-mode');
        const isNowDarkMode = body.classList.contains('dark-mode');

        // Update icon
        if (isNowDarkMode) {
            icon.classList.remove('fa-moon');
            icon.classList.add('fa-sun');
        } else {
            icon.classList.remove('fa-sun');
            icon.classList.add('fa-moon');
        }

        // Save preference
        localStorage.setItem('darkMode', isNowDarkMode);

        // Add a small animation effect
        darkModeToggle.style.transform = 'scale(0.9)';
        setTimeout(() => {
            darkModeToggle.style.transform = 'scale(1)';
        }, 150);
    });
}

// ===== LOGIN MODAL FUNCTIONALITY =====

function initLoginModal() {
    const loginBtn = document.getElementById('loginBtn');
    const loginModal = document.getElementById('loginModal');
    const closeModal = document.getElementById('closeModal');
    const loginForm = document.getElementById('loginForm');
    const ssoLoginBtn = document.getElementById('ssoLoginBtn');

    // Open modal when login button is clicked
    loginBtn.addEventListener('click', function(e) {
        e.preventDefault();
        openModal();
    });

    // Close modal when close button is clicked
    closeModal.addEventListener('click', function(e) {
        e.preventDefault();
        closeModalFunc();
    });

    // Close modal when clicking outside the modal container
    loginModal.addEventListener('click', function(e) {
        if (e.target === loginModal) {
            closeModalFunc();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && loginModal.classList.contains('active')) {
            closeModalFunc();
        }
    });

    // Handle traditional login form submission (UI only)
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // Simple validation for demo purposes
        if (!username || !password) {
            // Add error styling to empty fields
            if (!username) {
                document.getElementById('username').classList.add('error');
                setTimeout(() => {
                    document.getElementById('username').classList.remove('error');
                }, 3000);
            }
            if (!password) {
                document.getElementById('password').classList.add('error');
                setTimeout(() => {
                    document.getElementById('password').classList.remove('error');
                }, 3000);
            }
            return;
        }

        // Simulate login process (UI only)
        const submitBtn = loginForm.querySelector('.login-btn-primary');
        submitBtn.classList.add('loading');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Anmelden...';

        setTimeout(() => {
            // Reset button
            submitBtn.classList.remove('loading');
            submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Einloggen';

            // Show success message (you can customize this)
            alert('Login erfolgreich! (Demo - keine echte Anmeldung)');
            closeModalFunc();

            // Reset form
            loginForm.reset();
        }, 2000);
    });

    // Handle SSO login button (UI only)
    ssoLoginBtn.addEventListener('click', function(e) {
        e.preventDefault();

        // Simulate SSO redirect (UI only)
        ssoLoginBtn.classList.add('loading');
        ssoLoginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Weiterleitung zu IServ...';

        setTimeout(() => {
            // Reset button
            ssoLoginBtn.classList.remove('loading');
            ssoLoginBtn.innerHTML = '<i class="fas fa-building"></i> Mit IServ anmelden (SSO)';

            // Show redirect message (you can customize this)
            alert('Weiterleitung zu IServ SSO... (Demo - keine echte Weiterleitung)');
            closeModalFunc();
        }, 1500);
    });

    // Modal functions
    function openModal() {
        loginModal.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling

        // Focus on first input field
        setTimeout(() => {
            document.getElementById('username').focus();
        }, 300);
    }

    function closeModalFunc() {
        loginModal.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling

        // Reset form and remove any error states
        loginForm.reset();
        document.querySelectorAll('.form-input.error').forEach(input => {
            input.classList.remove('error');
        });

        // Reset button states
        const submitBtn = loginForm.querySelector('.login-btn-primary');
        const ssoBtn = document.getElementById('ssoLoginBtn');

        submitBtn.classList.remove('loading');
        submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Einloggen';

        ssoBtn.classList.remove('loading');
        ssoBtn.innerHTML = '<i class="fas fa-building"></i> Mit IServ anmelden (SSO)';
    }

    // Add input field enhancements
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        // Remove error class when user starts typing
        input.addEventListener('input', function() {
            this.classList.remove('error');
        });

        // Add floating label effect (optional enhancement)
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });
}

// Matchup menu configuration (easily expandable)
const matchupConfig = {
    match1: { team1: "7A", team2: "8B", key1: "odds1-1", key2: "odds1-2" },
    match2: { team1: "9C", team2: "10A", key1: "odds2-1", key2: "odds2-2" },
    match3: { team1: "Lehrerteam", team2: "12B", key1: "odds3-1", key2: "odds3-2" }
};

// Initialize hamburger menu functionality
function initMatchupMenu() {
    const hamburgerBtn = document.getElementById('matchupToggle');
    const dropdown = document.getElementById('matchupDropdown');
    const matchupOptions = document.querySelectorAll('.matchup-option');

    // Toggle dropdown
    hamburgerBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        hamburgerBtn.classList.toggle('active');
        dropdown.classList.toggle('active');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!hamburgerBtn.contains(e.target) && !dropdown.contains(e.target)) {
            hamburgerBtn.classList.remove('active');
            dropdown.classList.remove('active');
        }
    });

    // Handle matchup selection
    matchupOptions.forEach(option => {
        option.addEventListener('click', function() {
            const matchupId = this.dataset.matchup;
            const config = matchupConfig[matchupId];

            if (config) {
                // Update the bars to show selected matchup
                showMatchup(config);

                // Close dropdown
                hamburgerBtn.classList.remove('active');
                dropdown.classList.remove('active');
            }
        });
    });
}

// Show specific matchup in the bars
function showMatchup(config) {
    const team1Votes = votes[config.key1] || 0;
    const team2Votes = votes[config.key2] || 0;
    const total = team1Votes + team2Votes;

    // Update team labels
    const team1Label = document.querySelector('#results div:first-child span:first-child');
    const team2Label = document.querySelector('#results div:last-child span:first-child');

    if (team1Label) team1Label.textContent = config.team1 + ':';
    if (team2Label) team2Label.textContent = config.team2 + ':';

    // Calculate percentages - handle zero total case
    let team1Percent, team2Percent;

    if (total === 0) {
        team1Percent = 0;
        team2Percent = 0;
    } else {
        team1Percent = Math.round((team1Votes / total) * 100);
        team2Percent = Math.round((team2Votes / total) * 100);

        // Ensure percentages add up to 100% (handle rounding issues)
        if (team1Percent + team2Percent !== 100 && total > 0) {
            if (team1Votes > team2Votes) {
                team1Percent = 100 - team2Percent;
            } else {
                team2Percent = 100 - team1Percent;
            }
        }
    }

    // Update bars and percentages
    const barA = document.getElementById("barA");
    const barB = document.getElementById("barB");
    const percentA = document.getElementById("percentA");
    const percentB = document.getElementById("percentB");

    if (barA) barA.style.width = team1Percent + "%";
    if (barB) barB.style.width = team2Percent + "%";
    if (percentA) percentA.textContent = team1Percent + "%";
    if (percentB) percentB.textContent = team2Percent + "%";

    console.log(`Debug: ${config.team1} (${team1Votes}) vs ${config.team2} (${team2Votes}) = ${team1Percent}% vs ${team2Percent}%`);
}

// Initialize credits toggle functionality
function initCreditsToggle() {
    const toggleBtn = document.getElementById('creditsToggle');
    const creditsPanel = document.getElementById('creditsPanel');

    toggleBtn.addEventListener('click', function() {
        toggleBtn.classList.toggle('active');
        creditsPanel.classList.toggle('active');
    });
}

// Initialize all functionality when page loads
document.addEventListener('DOMContentLoaded', function() {
    initDarkMode();
    initLoginModal();
    initMatchupMenu();
    initCreditsToggle();

    // Show default matchup (7A vs 8B)
    showMatchup(matchupConfig.match1);
});

// Initialanzeige
updateBars();
updateOddsFields();