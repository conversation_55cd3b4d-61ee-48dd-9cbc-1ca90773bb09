<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pausenliga Tippspiel</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="media-quarys.css">
    <!-- Google Fonts: Poppins -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    <!-- Optional: Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    
    <div class="container">
    <img id="gauss-logo" src="Gauss-Logo-Europa-round-main.png">

    <header class="header-image">
    </header>

    <header class="hero" id="webseite-header">
        <h1>Willkommen zur Webseite der Pausenliga</h1>
        <p>Hier erfährst du alles über die Team der Pausen Liga am Carl Friedrich Gauss Gymnasium</p>
    </header>

    <!-- Top Navigation -->
    <nav class="nav">
        <div class="logo">Gauß Gymnasium Pausenliga</div>
        <div>
            <button class="nav-btn">Anmelden</button>
            <button class="nav-btn nav-btn-primary">Registrieren</button>
        </div>
    </nav>
    </div>

    <main class="main-content">
        <section class="info-section">
            <h2>Was ist die Pausenliga?</h2>
            <p>Die Pausenliga ist die Fußball-Liga des Carl Friedrich Gauss Gymnasiums.<p>
        </section>

        <section class="sso-container">
            <?php
            require_once 'vendor/autoload.php';

            session_start();

            // Google OAuth2 configuration
            $client = new Google_Client();
            $client->setClientId('YOUR_GOOGLE_CLIENT_ID');
            $client->setClientSecret('YOUR_GOOGLE_CLIENT_SECRET');
            $client->setRedirectUri('http://localhost/website-v1.1/website.php');
            $client->addScope('email');
            $client->addScope('profile');

            // Handle OAuth2 callback
            if (isset($_GET['code'])) {
                $token = $client->fetchAccessTokenWithAuthCode($_GET['code']);
                $client->setAccessToken($token);

                // Get user info
                $oauth2 = new Google_Service_Oauth2($client);
                $userInfo = $oauth2->userinfo->get();

                // Store user info in session
                $_SESSION['user'] = [
                    'id' => $userInfo->id,
                    'email' => $userInfo->email,
                    'name' => $userInfo->name,
                    'picture' => $userInfo->picture,
                ];

                // Redirect to the main page
                header('Location: website.php');
                exit();
            }

            // Check if the user is logged in
            if (isset($_SESSION['user'])) {
                $user = $_SESSION['user'];
                echo "<div class='user-info'>";
                echo "<p>Welcome, <strong>{$user['name']}</strong>!</p>";
                echo "<img src='{$user['picture']}' alt='Profile Picture' class='profile-picture'>";
                echo "<p>Email: {$user['email']}</p>";
                echo "<a href='logout.php' class='logout-button'>Logout</a>";
                echo "</div>";
            } else {
                $authUrl = $client->createAuthUrl();
                echo "<a href='$authUrl' class='login-button'>Login with Google</a>";
            }
            ?>
        </section>

        <section class="teams-section">
            <h2>Unsere Teams 2025</h2>
            <div class="team-card">
                <img src="Media/Bild-1.jpeg" alt="Team 7A" class="team-img">
                <h3>5A</h3>
                <p>Die jungen Talente der 7A sind heiß auf den Sieg!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-2.jpeg" alt="Team 7B" class="team-img">
                <h3>5B</h3>
                <p>Die 8B hat sich viel vorgenommen und will ganz oben mitspielen!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-3.jpeg" alt="Team 7C" class="team-img">
                <h3>5C</h3>
                <p>Die 9C ist bekannt für ihre Teamarbeit und Fairness.</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-4.jpeg" alt="Team 6A" class="team-img">
                <h3>6A</h3>
                <p>Die 10A hat Erfahrung und kämpft um jeden Punkt!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-5.jpeg" alt="Team 6B" class="team-img">
                <h3>6B</h3>
                <p>Unsere Lehrer geben alles, um die Schüler herauszufordern!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-6.jpeg" alt="Team 6C" class="team-img">
                <h3>6C</h3>
                <p>Die 12B ist das Team der Oberstufe und will den Titel holen!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-7.jpeg" alt="Team 6C" class="team-img">
                <h3>7A</h3>
                <p>Die 12B ist das Team der Oberstufe und will den Titel holen!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-8.jpeg" alt="Team 6C" class="team-img">
                <h3>7B</h3>
                <p>Die 12B ist das Team der Oberstufe und will den Titel holen!</p>
            </div>
            <div class="team-card">
                <img src="Media/Bild-9.jpeg" alt="Team 6C" class="team-img">
                <h3>7C</h3>
                <p>Die 12B ist das Team der Oberstufe und will den Titel holen!</p>
            </div>
        </section>
    </main>

    <!-- Hero Section -->
    <header class="hero">
        <h1>Tippe auf die Spiele der Pausenliga!</h1>
        <p>Wer holt den Sieg in der großen Pause? Gib deinen Tipp ab und vergleiche dich mit deinen Mitschülern – ganz ohne Einsatz, nur zum Spaß!</p>
        <button href="#tipp_abgeben" class="cta-btn">Jetzt Tipp abgeben!</button>
    </header>

    <!-- Live Betting Section -->
    <main>
        <section class="live-section">
            <h2>Aktuelle Pausenliga-Spiele</h2>
            <div class="live-card">
                <div>
                    <div class="match">7A vs. 8B</div>
                    <div class="sport">Fußball</div>
                </div>
                <div class="odds-group">
                    <div class="odd" id="odds1-1">0 Tipps für 7A</div>
                    <div class="odd" id="odds1-2">0 Tipps für 8B</div>
                </div>
            </div>
            <div class="live-card">
                <div>
                    <div class="match">9C vs. 10A</div>
                    <div class="sport">Fußball</div>
                </div>
                <div class="odds-group">
                    <div class="odd" id="odds2-1">0 Tipps für 9C</div>
                    <div class="odd" id="odds2-2">0 Tipps für 10A</div>
                </div>
            </div>
            <div class="live-card">
                <div>
                    <div class="match">Lehrerteam vs. 12B</div>
                    <div class="sport">Fußball</div>
                </div>
                <div class="odds-group">
                    <div class="odd" id="odds3-1">0 Tipps für Lehrerteam</div>
                    <div class="odd" id="odds3-2">0 Tipps für 12B</div>
                </div>
            </div>
        </section>

        <!-- Voting Section -->
        <section class="vote-section">
            <h2>Wer gewinnt das Topspiel?</h2>
            <form id="betForm">
                <input type="text" id="name" name="name" placeholder="Dein Name" required>
                <select id="team" name="team" required>
                    <option value="">--Team wählen--</option>
                    <option value="odds1-1">7A</option>
                    <option value="odds1-2">8B</option>
                    <option value="odds2-1">9C</option>
                    <option value="odds2-2">10A</option>
                    <option value="odds3-1">Lehrerteam</option>
                    <option value="odds3-2">12B</option>
                </select>
                <button class="tipp_abgeben" type="submit">Tipp abgeben</button>
            </form>
            <div id="results">
                <div>
                    <span>7A:</span>
                    <div class="bar-bg">
                        <div id="barA" class="bar"></div>
                    </div>
                    <span id="percentA">0%</span>
                </div>
                <div>
                    <span>8B:</span>
                    <div class="bar-bg">
                        <div id="barB" class="bar"></div>
                    </div>
                    <span id="percentB">0%</span>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer>
        <div>
            <a href="#">Regeln</a> | <a href="#">Kontakt</a> | <a href="#">Impressum</a>
        </div>
        <p>&copy; 2025 Pausenliga Tippspiel. Nur für Schüler:innen und Lehrer:innen der Schule. Kein echtes Geld, nur Spaß!</p>
        <p>&copy; Diese Webseite ist ein Opensource Project von Schülern des Informatik Kurses in der 9ten Stufe 2025.</p>
    </footer>
    <script src="website.js"></script>
</body>
</html>

